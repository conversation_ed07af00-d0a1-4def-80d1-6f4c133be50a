import React from 'react';
import { Button } from '../ui';
import { cn } from '../../utils';

interface CallToActionSectionProps {
  className?: string;
}

export const CallToActionSection: React.FC<CallToActionSectionProps> = ({ className }) => {
  return (
    <section className={cn('py-20 bg-gradient-to-r from-primary-600 to-primary-700', className)}>
      <div className="container-custom">
        <div className="max-w-4xl mx-auto text-center text-white">
          <h2 className="text-3xl md:text-4xl font-bold font-heading mb-6">
            Ready to Transform Your English Skills?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join over 10,000 students who have already improved their English proficiency 
            with our proven learning methodology. Start your journey today!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button 
              size="lg" 
              className="px-8 py-4 bg-white text-primary-600 hover:bg-gray-50 font-semibold"
            >
              Start Free Trial
            </Button>
            <Button 
              variant="outline" 
              size="lg" 
              className="px-8 py-4 border-white text-white hover:bg-white hover:text-primary-600 font-semibold"
            >
              View Pricing Plans
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 pt-8 border-t border-primary-500">
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">7-Day</div>
              <div className="text-sm opacity-80">Free Trial</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">No Credit Card</div>
              <div className="text-sm opacity-80">Required</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">Cancel</div>
              <div className="text-sm opacity-80">Anytime</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
