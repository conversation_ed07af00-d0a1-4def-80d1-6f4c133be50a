import React from 'react';
import { cn } from '../../utils';

interface CoursesSectionProps {
  className?: string;
}

interface CourseCategory {
  id: string;
  title: string;
  courseCount: number;
  color: string;
  size: 'small' | 'medium' | 'large';
}

const courseCategories: CourseCategory[] = [
  {
    id: '1',
    title: 'PROGRAMMING',
    courseCount: 22,
    color: 'bg-gradient-to-br from-blue-500 to-purple-600',
    size: 'medium',
  },
  {
    id: '2',
    title: 'DEVELOPMENT',
    courseCount: 15,
    color: 'bg-gradient-to-br from-green-400 to-green-600',
    size: 'small',
  },
  {
    id: '3',
    title: 'HEALTH',
    courseCount: 78,
    color: 'bg-gradient-to-br from-purple-500 to-purple-700',
    size: 'large',
  },
  {
    id: '4',
    title: 'INTERIOR',
    courseCount: 31,
    color: 'bg-gradient-to-br from-blue-400 to-blue-600',
    size: 'small',
  },
  {
    id: '5',
    title: 'MUSIC',
    courseCount: 12,
    color: 'bg-gradient-to-br from-orange-400 to-orange-600',
    size: 'small',
  },
  {
    id: '6',
    title: 'BUSINESS',
    courseCount: 34,
    color: 'bg-gradient-to-br from-blue-500 to-blue-700',
    size: 'medium',
  },
  {
    id: '7',
    title: 'DESIGN',
    courseCount: 84,
    color: 'bg-gradient-to-br from-red-400 to-red-600',
    size: 'small',
  },
  {
    id: '8',
    title: 'PHOTOGRAPHY',
    courseCount: 71,
    color: 'bg-gradient-to-br from-pink-400 to-pink-600',
    size: 'large',
  },
];

const getSizeClasses = (size: string) => {
  switch (size) {
    case 'small':
      return 'h-32 w-40';
    case 'medium':
      return 'h-40 w-48';
    case 'large':
      return 'h-48 w-40';
    default:
      return 'h-32 w-40';
  }
};

export const CoursesSection: React.FC<CoursesSectionProps> = ({ className }) => {
  return (
    <section className={cn('py-20 bg-gray-50', className)}>
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <p className="text-sm font-semibold text-primary-600 uppercase tracking-wider mb-4">
            START LEARNING TODAY
          </p>
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 font-heading mb-4">
            Pick A Courses From Category
          </h2>
          <p className="text-xl text-secondary-600 max-w-2xl mx-auto">
            At the Learno Education users can learn lots of things from real 
            nayto kolno or fera expert
          </p>
        </div>

        {/* Courses Grid */}
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 items-end justify-items-center">
            {/* Row 1 */}
            <div className="flex flex-col gap-4">
              {/* Programming */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[0].color,
                getSizeClasses(courseCategories[0].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[0].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[0].courseCount} Courses</p>
              </div>
              
              {/* Music */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[4].color,
                getSizeClasses(courseCategories[4].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[4].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[4].courseCount} Courses</p>
              </div>
            </div>

            {/* Row 2 */}
            <div className="flex flex-col gap-4">
              {/* Development */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[1].color,
                getSizeClasses(courseCategories[1].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[1].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[1].courseCount} Courses</p>
              </div>
              
              {/* Business */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[5].color,
                getSizeClasses(courseCategories[5].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[5].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[5].courseCount} Courses</p>
              </div>
            </div>

            {/* Row 3 */}
            <div className="flex flex-col gap-4">
              {/* Health */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[2].color,
                getSizeClasses(courseCategories[2].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[2].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[2].courseCount} Courses</p>
              </div>
              
              {/* Design */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[6].color,
                getSizeClasses(courseCategories[6].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[6].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[6].courseCount} Courses</p>
              </div>
            </div>

            {/* Row 4 */}
            <div className="flex flex-col gap-4">
              {/* Interior */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[3].color,
                getSizeClasses(courseCategories[3].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[3].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[3].courseCount} Courses</p>
              </div>
              
              {/* Photography */}
              <div className={cn(
                'rounded-2xl text-white p-6 flex flex-col justify-end cursor-pointer transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl',
                courseCategories[7].color,
                getSizeClasses(courseCategories[7].size)
              )}>
                <h3 className="text-lg font-bold mb-1">{courseCategories[7].title}</h3>
                <p className="text-sm opacity-90">{courseCategories[7].courseCount} Courses</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
