import React from 'react';
import { Card } from '../ui';
import { features } from '../../data';
import { cn } from '../../utils';

// SVG Icon Components with Colors - mga icons para sa features
const TargetIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="#3B82F6" fillOpacity="0.1" stroke="#3B82F6" strokeWidth="2"/>
    <path d="M9 12l2 2 4-4" stroke="#10B981" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const BookIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20" stroke="#8B5CF6" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z" fill="#8B5CF6" fillOpacity="0.1" stroke="#8B5CF6" strokeWidth="2"/>
    <path d="M8 7h8M8 11h6" stroke="#8B5CF6" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

const LightningIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="#F59E0B" stroke="#F59E0B" strokeWidth="2" strokeLinejoin="round"/>
    <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="url(#lightning-gradient)"/>
    <defs>
      <linearGradient id="lightning-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#FCD34D"/>
        <stop offset="100%" stopColor="#F59E0B"/>
      </linearGradient>
    </defs>
  </svg>
);

const ChartIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="3" y="12" width="4" height="9" fill="#06B6D4" rx="1"/>
    <rect x="10" y="8" width="4" height="13" fill="#3B82F6" rx="1"/>
    <rect x="17" y="4" width="4" height="17" fill="#8B5CF6" rx="1"/>
    <path d="M3 3v18h18" stroke="#64748B" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const UsersIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <circle cx="9" cy="7" r="4" fill="#EC4899" fillOpacity="0.2" stroke="#EC4899" strokeWidth="2"/>
    <circle cx="15" cy="7" r="4" fill="#06B6D4" fillOpacity="0.2" stroke="#06B6D4" strokeWidth="2"/>
    <path d="M1 21v-2a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v2" stroke="#EC4899" strokeWidth="2" strokeLinecap="round"/>
    <path d="M15 21v-2a4 4 0 0 1 4-4h4" stroke="#06B6D4" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

const DeviceIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="none">
    <rect x="5" y="2" width="14" height="20" rx="3" fill="url(#device-gradient)" stroke="#6366F1" strokeWidth="2"/>
    <rect x="7" y="5" width="10" height="12" fill="#F8FAFC" rx="1"/>
    <circle cx="12" cy="19" r="1.5" fill="#6366F1"/>
    <defs>
      <linearGradient id="device-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="#E0E7FF"/>
        <stop offset="100%" stopColor="#C7D2FE"/>
      </linearGradient>
    </defs>
  </svg>
);

// Icon mapping
const iconMap: { [key: string]: React.ComponentType } = {
  '🎯': TargetIcon,
  '📚': BookIcon,
  '⚡': LightningIcon,
  '📊': ChartIcon,
  '👥': UsersIcon,
  '📱': DeviceIcon,
};

interface FeaturesSectionProps {
  className?: string;
}

export const FeaturesSection: React.FC<FeaturesSectionProps> = ({ className }) => {
  return (
    <section id="features" className={cn('py-20 bg-white', className)}>
      <div className="container-custom">
        {/* Section Header - title ug description */}
        
        <div className="text-center mb-16">

          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 font-heading mb-4">
            Why Choose Our ESL Platform?
          </h2>
          
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Discover the features that make our platform the perfect choice for 
            English language learners worldwide.
          </p>

        </div>

        {/* Features Grid - display sa mga features */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature) => {
            const IconComponent = iconMap[feature.icon] || TargetIcon;
            return (
              <Card
                key={feature.id}
                className="text-center hover:shadow-lg transition-shadow duration-300 group"
                variant="default"
              >
                <div className="mb-6">
                  <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-gray-50 transition-all duration-300 shadow-lg group-hover:shadow-xl group-hover:scale-105">
                    <IconComponent />
                  </div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-secondary-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </Card>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-secondary-900 mb-4">
              Ready to Start Your Learning Journey?
            </h3>
            <p className="text-lg text-secondary-600 mb-8 max-w-2xl mx-auto">
              Join thousands of students who have already improved their English skills 
              with our comprehensive learning platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary px-8 py-3 text-lg">
                Get Started Today
              </button>
              <button className="btn-secondary px-8 py-3 text-lg">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
